use crate::communication::channels::ChannelManager;
use crate::communication::message::{Message, MessageAck, MessageId, Priority};
use crate::{BacktestError, Result};
use std::collections::HashMap;
use std::sync::Arc;
use tokio::sync::{broadcast, mpsc, Mutex};
use tokio::time::{interval, Duration, Instant};
use tracing::{debug, error, info, warn};

/// 消息总线
pub struct MessageBus {
    /// 通道管理器
    channel_manager: Arc<Mutex<ChannelManager>>,
    /// 待确认消息
    pending_acks: Arc<Mutex<HashMap<MessageId, PendingMessage>>>,
    /// 消息统计
    stats: Arc<Mutex<MessageStats>>,
    /// 确认超时时间
    ack_timeout: Duration,
}

/// 待确认消息
#[derive(Debug, Clone)]
struct PendingMessage {
    message: Message,
    sent_at: Instant,
    retry_count: u32,
}

/// 消息统计
#[derive(Debu<PERSON>, <PERSON><PERSON>, <PERSON><PERSON>ult)]
pub struct MessageStats {
    pub total_sent: u64,
    pub total_received: u64,
    pub total_broadcast: u64,
    pub total_acks: u64,
    pub failed_sends: u64,
    pub expired_messages: u64,
    pub retry_attempts: u64,
}

impl MessageBus {
    /// 创建新的消息总线
    pub fn new() -> Self {
        Self {
            channel_manager: Arc::new(Mutex::new(ChannelManager::new(1000))),
            pending_acks: Arc::new(Mutex::new(HashMap::new())),
            stats: Arc::new(Mutex::new(MessageStats::default())),
            ack_timeout: Duration::from_secs(30),
        }
    }

    /// 设置确认超时时间
    pub fn with_ack_timeout(mut self, timeout: Duration) -> Self {
        self.ack_timeout = timeout;
        self
    }

    /// 启动消息总线
    pub async fn start(&self) -> Result<()> {
        info!("Starting message bus");

        // 启动确认超时检查任务
        let pending_acks = Arc::clone(&self.pending_acks);
        let stats = Arc::clone(&self.stats);
        let timeout = self.ack_timeout;

        tokio::spawn(async move {
            let mut interval = interval(Duration::from_secs(5));

            loop {
                interval.tick().await;
                Self::check_ack_timeouts(pending_acks.clone(), stats.clone(), timeout).await;
            }
        });

        info!("Message bus started");
        Ok(())
    }

    /// 创建广播通道
    pub async fn create_broadcast_channel(&self, name: String) -> broadcast::Receiver<Message> {
        let mut manager = self.channel_manager.lock().await;
        manager.create_broadcast_channel(name)
    }

    /// 创建单向通道
    pub async fn create_mpsc_channel(&self, name: String) -> mpsc::Receiver<Message> {
        let mut manager = self.channel_manager.lock().await;
        manager.create_mpsc_channel(name)
    }

    /// 订阅广播通道
    pub async fn subscribe(&self, channel_name: &str) -> Option<broadcast::Receiver<Message>> {
        let manager = self.channel_manager.lock().await;
        manager.subscribe_broadcast(channel_name)
    }

    /// 发送广播消息
    pub async fn broadcast(&self, channel_name: &str, message: Message) -> Result<usize> {
        let manager = self.channel_manager.lock().await;

        // 检查消息是否过期
        if message.is_expired() {
            self.increment_stat("expired_messages").await;
            return Err(BacktestError::Communication("Message expired".to_string()));
        }

        match manager.broadcast(channel_name, message.clone()).await {
            Ok(receiver_count) => {
                self.increment_stat("total_broadcast").await;
                self.increment_stat("total_sent").await;

                // 如果需要确认，添加到待确认列表
                if message.requires_ack {
                    self.add_pending_ack(message.clone()).await;
                }

                debug!(
                    "Broadcasted message {} to {} receivers",
                    message.id, receiver_count
                );
                Ok(receiver_count)
            }
            Err(e) => {
                self.increment_stat("failed_sends").await;
                Err(e)
            }
        }
    }

    /// 发送点对点消息
    pub async fn send(&self, channel_name: &str, message: Message) -> Result<()> {
        let manager = self.channel_manager.lock().await;

        // 检查消息是否过期
        if message.is_expired() {
            self.increment_stat("expired_messages").await;
            return Err(BacktestError::Communication("Message expired".to_string()));
        }

        match manager.send(channel_name, message.clone()).await {
            Ok(()) => {
                self.increment_stat("total_sent").await;

                // 如果需要确认，添加到待确认列表
                if message.requires_ack {
                    self.add_pending_ack(message.clone()).await;
                }

                debug!("Sent message {} to channel {}", message.id, channel_name);
                Ok(())
            }
            Err(e) => {
                self.increment_stat("failed_sends").await;
                Err(e)
            }
        }
    }

    /// 发送消息确认
    pub async fn send_ack(&self, ack: MessageAck) -> Result<()> {
        // 从待确认列表中移除消息
        let mut pending = self.pending_acks.lock().await;
        if pending.remove(&ack.message_id).is_some() {
            self.increment_stat("total_acks").await;
            debug!("Received ack for message {}", ack.message_id);
        } else {
            warn!("Received ack for unknown message {}", ack.message_id);
        }

        Ok(())
    }

    /// 添加待确认消息
    async fn add_pending_ack(&self, message: Message) {
        let mut pending = self.pending_acks.lock().await;
        pending.insert(
            message.id,
            PendingMessage {
                message,
                sent_at: Instant::now(),
                retry_count: 0,
            },
        );
    }

    /// 检查确认超时
    async fn check_ack_timeouts(
        pending_acks: Arc<Mutex<HashMap<MessageId, PendingMessage>>>,
        stats: Arc<Mutex<MessageStats>>,
        timeout: Duration,
    ) {
        let mut pending = pending_acks.lock().await;
        let now = Instant::now();
        let mut expired_messages = Vec::new();

        for (message_id, pending_msg) in pending.iter() {
            if now.duration_since(pending_msg.sent_at) > timeout {
                expired_messages.push(*message_id);
            }
        }

        for message_id in expired_messages {
            if let Some(pending_msg) = pending.remove(&message_id) {
                warn!(
                    "Message {} timed out after {} retries",
                    message_id, pending_msg.retry_count
                );

                // 更新统计
                let mut stats = stats.lock().await;
                stats.expired_messages += 1;
            }
        }
    }

    /// 增加统计计数
    async fn increment_stat(&self, stat_name: &str) {
        let mut stats = self.stats.lock().await;
        match stat_name {
            "total_sent" => stats.total_sent += 1,
            "total_received" => stats.total_received += 1,
            "total_broadcast" => stats.total_broadcast += 1,
            "total_acks" => stats.total_acks += 1,
            "failed_sends" => stats.failed_sends += 1,
            "expired_messages" => stats.expired_messages += 1,
            "retry_attempts" => stats.retry_attempts += 1,
            _ => warn!("Unknown stat: {}", stat_name),
        }
    }

    /// 获取消息统计
    pub async fn get_stats(&self) -> MessageStats {
        let stats = self.stats.lock().await;
        stats.clone()
    }

    /// 获取待确认消息数量
    pub async fn pending_ack_count(&self) -> usize {
        let pending = self.pending_acks.lock().await;
        pending.len()
    }

    /// 清空待确认消息
    pub async fn clear_pending_acks(&self) {
        let mut pending = self.pending_acks.lock().await;
        let count = pending.len();
        pending.clear();
        info!("Cleared {} pending acknowledgments", count);
    }

    /// 重置统计信息
    pub async fn reset_stats(&self) {
        let mut stats = self.stats.lock().await;
        *stats = MessageStats::default();
        info!("Reset message bus statistics");
    }
}

impl Default for MessageBus {
    fn default() -> Self {
        Self::new()
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::communication::message::{ControlMessage, MessageType};

    #[tokio::test]
    async fn test_message_bus_broadcast() {
        let bus = MessageBus::new();
        bus.start().await.unwrap();

        // 创建广播通道
        let mut rx = bus.create_broadcast_channel("test".to_string()).await;

        // 发送广播消息
        let message = Message::broadcast(
            MessageType::Control(ControlMessage::Shutdown),
            "test_sender".to_string(),
        );

        let result = bus.broadcast("test", message.clone()).await;
        assert!(result.is_ok());

        // 接收消息
        let received = rx.recv().await.unwrap();
        assert_eq!(received.id, message.id);

        // 检查统计
        let stats = bus.get_stats().await;
        assert_eq!(stats.total_broadcast, 1);
        assert_eq!(stats.total_sent, 1);
    }

    #[tokio::test]
    async fn test_message_bus_send() {
        let bus = MessageBus::new();
        bus.start().await.unwrap();

        // 创建单向通道
        let mut rx = bus.create_mpsc_channel("test".to_string()).await;

        // 发送消息
        let message = Message::to(
            MessageType::Control(ControlMessage::HealthCheck {
                module: "test".to_string(),
            }),
            "sender".to_string(),
            "receiver".to_string(),
        );

        let result = bus.send("test", message.clone()).await;
        assert!(result.is_ok());

        // 接收消息
        let received = rx.recv().await.unwrap();
        assert_eq!(received.id, message.id);

        // 检查统计
        let stats = bus.get_stats().await;
        assert_eq!(stats.total_sent, 1);
    }
}
