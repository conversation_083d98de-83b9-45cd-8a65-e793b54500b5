use crate::config::ConfigManager;
use crate::types::{BookTicker, MarketData, Price, TimeBarrier};
use crate::{BacktestError, Result};
use chrono::{DateTime, Utc};
use std::path::PathBuf;
use tokio::fs::File;
use tokio::io::{AsyncBufReadExt, BufReader};
use tokio::sync::mpsc;
use tracing::{debug, error, info};

/// 数据读取器
pub struct DataReader {
    data_path: PathBuf,
    current_barrier: Option<TimeBarrier>,
}

impl DataReader {
    /// 创建新的数据读取器
    pub fn new() -> Result<Self> {
        let config = ConfigManager::get()?;
        Ok(Self {
            data_path: config.data_path,
            current_barrier: None,
        })
    }

    /// 从指定路径创建数据读取器
    pub fn with_path(data_path: PathBuf) -> Self {
        Self {
            data_path,
            current_barrier: None,
        }
    }

    /// 开始读取数据
    pub async fn start_reading(&mut self, output_tx: mpsc::Sender<MarketData>) -> Result<()> {
        info!("Starting data reading from path: {:?}", self.data_path);

        let config = ConfigManager::get()?;

        // 读取不同类型的数据文件
        self.read_orderbook_data(&output_tx, &config.start_time, &config.end_time)
            .await?;
        self.read_bbo_data(&output_tx, &config.start_time, &config.end_time)
            .await?;
        self.read_trade_data(&output_tx, &config.start_time, &config.end_time)
            .await?;
        self.read_bookticker_data(&output_tx, &config.start_time, &config.end_time)
            .await?;

        info!("Data reading completed");
        Ok(())
    }

    /// 读取订单簿数据
    async fn read_orderbook_data(
        &self,
        output_tx: &mpsc::Sender<MarketData>,
        start_time: &DateTime<Utc>,
        end_time: &DateTime<Utc>,
    ) -> Result<()> {
        let orderbook_path = self.data_path.join("orderbook.jsonl");
        if !orderbook_path.exists() {
            debug!("Orderbook data file not found: {:?}", orderbook_path);
            return Ok(());
        }

        info!("Reading orderbook data from: {:?}", orderbook_path);

        let file = File::open(&orderbook_path)
            .await
            .map_err(|e| BacktestError::Data(format!("Failed to open orderbook file: {}", e)))?;

        let reader = BufReader::new(file);
        let mut lines = reader.lines();

        while let Some(line) = lines
            .next_line()
            .await
            .map_err(|e| BacktestError::Data(format!("Failed to read line: {}", e)))?
        {
            // 解析订单簿数据
            match self.parse_orderbook_line(&line) {
                Ok(Some(market_data)) => {
                    if self.is_within_time_range(&market_data, start_time, end_time) {
                        if let Err(e) = output_tx.send(market_data).await {
                            error!("Failed to send orderbook data: {}", e);
                            break;
                        }
                    }
                }
                Ok(None) => continue,
                Err(e) => {
                    error!("Failed to parse orderbook line: {}", e);
                    continue;
                }
            }
        }

        Ok(())
    }

    /// 读取BBO数据
    async fn read_bbo_data(
        &self,
        output_tx: &mpsc::Sender<MarketData>,
        start_time: &DateTime<Utc>,
        end_time: &DateTime<Utc>,
    ) -> Result<()> {
        let bbo_path = self.data_path.join("bbo.jsonl");
        if !bbo_path.exists() {
            debug!("BBO data file not found: {:?}", bbo_path);
            return Ok(());
        }

        info!("Reading BBO data from: {:?}", bbo_path);

        let file = File::open(&bbo_path)
            .await
            .map_err(|e| BacktestError::Data(format!("Failed to open BBO file: {}", e)))?;

        let reader = BufReader::new(file);
        let mut lines = reader.lines();

        while let Some(line) = lines
            .next_line()
            .await
            .map_err(|e| BacktestError::Data(format!("Failed to read line: {}", e)))?
        {
            // 解析BBO数据
            match self.parse_bbo_line(&line) {
                Ok(Some(market_data)) => {
                    if self.is_within_time_range(&market_data, start_time, end_time) {
                        if let Err(e) = output_tx.send(market_data).await {
                            error!("Failed to send BBO data: {}", e);
                            break;
                        }
                    }
                }
                Ok(None) => continue,
                Err(e) => {
                    error!("Failed to parse BBO line: {}", e);
                    continue;
                }
            }
        }

        Ok(())
    }

    /// 读取交易数据
    async fn read_trade_data(
        &self,
        output_tx: &mpsc::Sender<MarketData>,
        start_time: &DateTime<Utc>,
        end_time: &DateTime<Utc>,
    ) -> Result<()> {
        let trade_path = self.data_path.join("trades.jsonl");
        if !trade_path.exists() {
            debug!("Trade data file not found: {:?}", trade_path);
            return Ok(());
        }

        info!("Reading trade data from: {:?}", trade_path);

        let file = File::open(&trade_path)
            .await
            .map_err(|e| BacktestError::Data(format!("Failed to open trade file: {}", e)))?;

        let reader = BufReader::new(file);
        let mut lines = reader.lines();

        while let Some(line) = lines
            .next_line()
            .await
            .map_err(|e| BacktestError::Data(format!("Failed to read line: {}", e)))?
        {
            // 解析交易数据
            match self.parse_trade_line(&line) {
                Ok(Some(market_data)) => {
                    if self.is_within_time_range(&market_data, start_time, end_time) {
                        if let Err(e) = output_tx.send(market_data).await {
                            error!("Failed to send trade data: {}", e);
                            break;
                        }
                    }
                }
                Ok(None) => continue,
                Err(e) => {
                    error!("Failed to parse trade line: {}", e);
                    continue;
                }
            }
        }

        Ok(())
    }

    /// 读取BookTicker数据（CSV格式）
    async fn read_bookticker_data(
        &self,
        output_tx: &mpsc::Sender<MarketData>,
        start_time: &DateTime<Utc>,
        end_time: &DateTime<Utc>,
    ) -> Result<()> {
        // 查找CSV格式的BookTicker文件
        let bookticker_files = self.find_bookticker_files().await?;

        if bookticker_files.is_empty() {
            debug!("No BookTicker CSV files found in: {:?}", self.data_path);
            return Ok(());
        }

        for file_path in bookticker_files {
            info!("Reading BookTicker data from: {:?}", file_path);

            let file = File::open(&file_path).await.map_err(|e| {
                BacktestError::Data(format!("Failed to open BookTicker file: {}", e))
            })?;

            let reader = BufReader::new(file);
            let mut lines = reader.lines();

            // 跳过CSV头部
            if let Some(_header) = lines
                .next_line()
                .await
                .map_err(|e| BacktestError::Data(format!("Failed to read header: {}", e)))?
            {
                debug!("Skipped CSV header");
            }

            while let Some(line) = lines
                .next_line()
                .await
                .map_err(|e| BacktestError::Data(format!("Failed to read line: {}", e)))?
            {
                // 解析BookTicker数据
                match self.parse_bookticker_csv_line(&line) {
                    Ok(Some(market_data)) => {
                        if self.is_within_time_range(&market_data, start_time, end_time) {
                            if let Err(e) = output_tx.send(market_data).await {
                                error!("Failed to send BookTicker data: {}", e);
                                break;
                            }
                        }
                    }
                    Ok(None) => continue,
                    Err(e) => {
                        error!("Failed to parse BookTicker line: {}", e);
                        continue;
                    }
                }
            }
        }

        Ok(())
    }

    /// 解析订单簿行数据（占位实现）
    fn parse_orderbook_line(&self, _line: &str) -> Result<Option<MarketData>> {
        // TODO: 实现具体的解析逻辑
        Ok(None)
    }

    /// 解析BBO行数据（占位实现）
    fn parse_bbo_line(&self, _line: &str) -> Result<Option<MarketData>> {
        // TODO: 实现具体的解析逻辑
        Ok(None)
    }

    /// 解析交易行数据（占位实现）
    fn parse_trade_line(&self, _line: &str) -> Result<Option<MarketData>> {
        // TODO: 实现具体的解析逻辑
        Ok(None)
    }

    /// 查找BookTicker CSV文件
    async fn find_bookticker_files(&self) -> Result<Vec<PathBuf>> {
        let mut files = Vec::new();

        // 读取数据目录
        let mut dir = tokio::fs::read_dir(&self.data_path)
            .await
            .map_err(|e| BacktestError::Data(format!("Failed to read data directory: {}", e)))?;

        while let Some(entry) = dir
            .next_entry()
            .await
            .map_err(|e| BacktestError::Data(format!("Failed to read directory entry: {}", e)))?
        {
            let path = entry.path();
            if let Some(file_name) = path.file_name().and_then(|n| n.to_str()) {
                // 查找包含"bookTicker"的CSV文件
                if file_name.contains("bookTicker") && file_name.ends_with(".csv") {
                    files.push(path);
                }
            }
        }

        // 按文件名排序以确保一致的处理顺序
        files.sort();

        Ok(files)
    }

    /// 解析BookTicker CSV行数据
    fn parse_bookticker_csv_line(&self, line: &str) -> Result<Option<MarketData>> {
        if line.trim().is_empty() {
            return Ok(None);
        }

        let fields: Vec<&str> = line.split(',').collect();
        if fields.len() != 7 {
            return Err(BacktestError::Data(format!(
                "Invalid BookTicker CSV format: expected 7 fields, got {}",
                fields.len()
            )));
        }

        // CSV格式: update_id,best_bid_price,best_bid_qty,best_ask_price,best_ask_qty,transaction_time,event_time
        let update_id: u64 = fields[0]
            .parse()
            .map_err(|e| BacktestError::Data(format!("Invalid update_id: {}", e)))?;

        let best_bid_price: f64 = fields[1]
            .parse()
            .map_err(|e| BacktestError::Data(format!("Invalid best_bid_price: {}", e)))?;

        let best_bid_qty: f64 = fields[2]
            .parse()
            .map_err(|e| BacktestError::Data(format!("Invalid best_bid_qty: {}", e)))?;

        let best_ask_price: f64 = fields[3]
            .parse()
            .map_err(|e| BacktestError::Data(format!("Invalid best_ask_price: {}", e)))?;

        let best_ask_qty: f64 = fields[4]
            .parse()
            .map_err(|e| BacktestError::Data(format!("Invalid best_ask_qty: {}", e)))?;

        let transaction_time: u64 = fields[5]
            .parse()
            .map_err(|e| BacktestError::Data(format!("Invalid transaction_time: {}", e)))?;

        let event_time: u64 = fields[6]
            .parse()
            .map_err(|e| BacktestError::Data(format!("Invalid event_time: {}", e)))?;

        let bookticker = BookTicker::new(
            update_id,
            Price::new(best_bid_price),
            best_bid_qty,
            Price::new(best_ask_price),
            best_ask_qty,
            transaction_time,
            event_time,
        );

        Ok(Some(MarketData::BookTicker(bookticker)))
    }

    /// 检查数据是否在时间范围内
    fn is_within_time_range(
        &self,
        market_data: &MarketData,
        start_time: &DateTime<Utc>,
        end_time: &DateTime<Utc>,
    ) -> bool {
        match market_data {
            MarketData::OrderBook(snapshot) => {
                snapshot.timestamp >= *start_time && snapshot.timestamp <= *end_time
            }
            MarketData::Trade(trade) => {
                if let Some(timestamp) = trade.timestamp {
                    timestamp >= *start_time && timestamp <= *end_time
                } else {
                    true // 如果没有时间戳，默认通过
                }
            }
            MarketData::BookTicker(bookticker) => {
                let event_datetime = bookticker.event_datetime();
                event_datetime >= *start_time && event_datetime <= *end_time
            }
            MarketData::Bbo(_) => {
                // BBO数据通常没有时间戳，使用update_id作为时间屏障
                // 这里暂时返回true，实际应该基于时间屏障判断
                true
            }
        }
    }

    /// 更新时间屏障
    pub fn update_time_barrier(&mut self, barrier: TimeBarrier) {
        self.current_barrier = Some(barrier);
    }

    /// 获取当前时间屏障
    pub fn get_current_barrier(&self) -> Option<&TimeBarrier> {
        self.current_barrier.as_ref()
    }
}
