use crate::matching::OrderBook;
use crate::types::{Bbo, MarketData, Order, OrderSide, OrderStatus, OrderType, Price, Trade};
use crate::{BacktestError, Result};
use std::collections::HashMap;
use tokio::sync::{broadcast, mpsc};
use tracing::{debug, error, info, warn};

/// 撮合引擎
pub struct MatchingEngine {
    /// 订单簿
    orderbook: OrderBook,
    /// 待处理订单
    pending_orders: HashMap<String, Order>,
    /// 市场数据输入
    market_data_rx: broadcast::Receiver<MarketData>,
    /// 订单输入
    order_rx: mpsc::Receiver<Order>,
    /// 成交输出
    trade_tx: broadcast::Sender<Trade>,
    /// 订单状态更新输出
    order_update_tx: broadcast::Sender<Order>,
}

impl MatchingEngine {
    /// 创建新的撮合引擎
    pub fn new(
        market_data_rx: broadcast::Receiver<MarketData>,
        order_rx: mpsc::Receiver<Order>,
        trade_tx: broadcast::Sender<Trade>,
        order_update_tx: broadcast::Sender<Order>,
    ) -> Self {
        Self {
            orderbook: OrderBook::new(),
            pending_orders: HashMap::new(),
            market_data_rx,
            order_rx,
            trade_tx,
            order_update_tx,
        }
    }

    /// 启动撮合引擎
    pub async fn start(&mut self) -> Result<()> {
        info!("Starting matching engine");

        loop {
            tokio::select! {
                // 处理市场数据
                market_data = self.market_data_rx.recv() => {
                    match market_data {
                        Ok(data) => {
                            if let Err(e) = self.process_market_data(data).await {
                                error!("Failed to process market data: {}", e);
                            }
                        }
                        Err(broadcast::error::RecvError::Closed) => {
                            info!("Market data channel closed");
                            break;
                        }
                        Err(broadcast::error::RecvError::Lagged(skipped)) => {
                            warn!("Market data lagged, skipped {} messages", skipped);
                        }
                    }
                }

                // 处理新订单
                order = self.order_rx.recv() => {
                    match order {
                        Some(order) => {
                            if let Err(e) = self.process_order(order).await {
                                error!("Failed to process order: {}", e);
                            }
                        }
                        None => {
                            info!("Order channel closed");
                            break;
                        }
                    }
                }
            }
        }

        info!("Matching engine stopped");
        Ok(())
    }

    /// 处理市场数据
    async fn process_market_data(&mut self, market_data: MarketData) -> Result<()> {
        match market_data {
            MarketData::OrderBook(snapshot) => {
                debug!("Processing orderbook snapshot");
                // 重建订单簿
                self.orderbook
                    .rebuild_from_snapshot(&snapshot.bids, &snapshot.asks);
                // 尝试撮合待处理订单
                self.match_pending_orders().await?;
            }
            MarketData::Bbo(bbo) => {
                debug!("Processing BBO update");
                // 使用BBO进行撮合
                self.match_with_bbo(&bbo).await?;
            }
            MarketData::Trade(trade) => {
                debug!("Processing external trade: {}", trade.id);
                // 外部交易可能影响订单簿状态
                // 这里可以添加相应的处理逻辑
            }
        }

        Ok(())
    }

    /// 处理新订单
    async fn process_order(&mut self, mut order: Order) -> Result<()> {
        info!(
            "Processing order: {} {} {} @ {:?}",
            order.id,
            match order.side {
                OrderSide::Buy => "BUY",
                OrderSide::Sell => "SELL",
            },
            order.quantity,
            order.price
        );

        match order.order_type {
            OrderType::Market => {
                // 市价单立即撮合
                self.match_market_order(&mut order).await?;
            }
            OrderType::Limit => {
                // 限价单先尝试撮合，未成交部分加入订单簿
                self.match_limit_order(&mut order).await?;
            }
        }

        Ok(())
    }

    /// 撮合市价单
    async fn match_market_order(&mut self, order: &mut Order) -> Result<()> {
        let mut remaining_quantity = order.quantity;

        match order.side {
            OrderSide::Buy => {
                // 买入市价单与卖单撮合
                if let Some(best_ask) = self.orderbook.best_ask() {
                    let available_quantity = self.orderbook.ask_depth(best_ask);
                    let trade_quantity = remaining_quantity.min(available_quantity);

                    if trade_quantity > 0.0 {
                        self.execute_trade(order, best_ask, trade_quantity).await?;
                        remaining_quantity -= trade_quantity;
                    }
                }
            }
            OrderSide::Sell => {
                // 卖出市价单与买单撮合
                if let Some(best_bid) = self.orderbook.best_bid() {
                    let available_quantity = self.orderbook.bid_depth(best_bid);
                    let trade_quantity = remaining_quantity.min(available_quantity);

                    if trade_quantity > 0.0 {
                        self.execute_trade(order, best_bid, trade_quantity).await?;
                        remaining_quantity -= trade_quantity;
                    }
                }
            }
        }

        // 更新订单状态
        if remaining_quantity <= 0.0 {
            order.status = OrderStatus::Filled;
        } else {
            order.status = OrderStatus::PartiallyFilled;
            order.quantity = remaining_quantity;
        }

        self.send_order_update(order.clone()).await?;
        Ok(())
    }

    /// 撮合限价单
    async fn match_limit_order(&mut self, order: &mut Order) -> Result<()> {
        let order_price = order
            .price
            .ok_or_else(|| BacktestError::Matching("Limit order must have price".to_string()))?;

        let mut remaining_quantity = order.quantity;

        match order.side {
            OrderSide::Buy => {
                // 买入限价单：如果价格 >= 最佳卖价，则可以撮合
                while let Some(best_ask) = self.orderbook.best_ask() {
                    if order_price >= best_ask && remaining_quantity > 0.0 {
                        let available_quantity = self.orderbook.ask_depth(best_ask);
                        let trade_quantity = remaining_quantity.min(available_quantity);

                        self.execute_trade(order, best_ask, trade_quantity).await?;
                        remaining_quantity -= trade_quantity;
                    } else {
                        break;
                    }
                }
            }
            OrderSide::Sell => {
                // 卖出限价单：如果价格 <= 最佳买价，则可以撮合
                while let Some(best_bid) = self.orderbook.best_bid() {
                    if order_price <= best_bid && remaining_quantity > 0.0 {
                        let available_quantity = self.orderbook.bid_depth(best_bid);
                        let trade_quantity = remaining_quantity.min(available_quantity);

                        self.execute_trade(order, best_bid, trade_quantity).await?;
                        remaining_quantity -= trade_quantity;
                    } else {
                        break;
                    }
                }
            }
        }

        // 更新订单状态
        if remaining_quantity <= 0.0 {
            order.status = OrderStatus::Filled;
        } else if remaining_quantity < order.quantity {
            order.status = OrderStatus::PartiallyFilled;
            order.quantity = remaining_quantity;
            // 未成交部分加入订单簿
            self.orderbook.add_order(order.clone());
            self.pending_orders.insert(order.id.clone(), order.clone());
        } else {
            // 完全未成交，直接加入订单簿
            order.status = OrderStatus::Pending;
            self.orderbook.add_order(order.clone());
            self.pending_orders.insert(order.id.clone(), order.clone());
        }

        self.send_order_update(order.clone()).await?;
        Ok(())
    }

    /// 使用BBO进行撮合
    async fn match_with_bbo(&mut self, bbo: &Bbo) -> Result<()> {
        // 收集需要处理的订单信息
        let mut orders_to_process = Vec::new();

        for (order_id, order) in &self.pending_orders {
            let order_price = order.price.unwrap_or(Price::new(0.0));
            let can_match = match order.side {
                OrderSide::Buy => order_price >= bbo.ask_price,
                OrderSide::Sell => order_price <= bbo.bid_price,
            };

            if can_match {
                orders_to_process.push((order_id.clone(), order.clone()));
            }
        }

        // 处理匹配的订单
        let mut orders_to_remove = Vec::new();

        for (order_id, mut order) in orders_to_process {
            let trade_price = match order.side {
                OrderSide::Buy => bbo.ask_price,
                OrderSide::Sell => bbo.bid_price,
            };

            let available_quantity = match order.side {
                OrderSide::Buy => bbo.ask_quantity,
                OrderSide::Sell => bbo.bid_quantity,
            };

            let trade_quantity = order.quantity.min(available_quantity);
            if trade_quantity > 0.0 {
                // 执行交易
                self.execute_trade(&order, trade_price, trade_quantity)
                    .await?;

                // 更新订单状态
                order.quantity -= trade_quantity;
                if order.quantity <= 0.0 {
                    order.status = OrderStatus::Filled;
                    orders_to_remove.push(order_id.clone());
                } else {
                    order.status = OrderStatus::PartiallyFilled;
                }

                // 更新订单到pending_orders
                if let Some(pending_order) = self.pending_orders.get_mut(&order_id) {
                    *pending_order = order.clone();
                }

                // 发送订单更新
                self.send_order_update(order).await?;
            }
        }

        // 移除已完成的订单
        for order_id in orders_to_remove {
            self.pending_orders.remove(&order_id);
            self.orderbook.remove_order(&order_id);
        }

        Ok(())
    }

    /// 撮合待处理订单
    async fn match_pending_orders(&mut self) -> Result<()> {
        // 这里可以实现更复杂的撮合逻辑
        // 目前只是一个占位实现
        Ok(())
    }

    /// 执行交易
    async fn execute_trade(&self, order: &Order, price: Price, quantity: f64) -> Result<()> {
        let trade = Trade {
            id: format!("{}_{}", order.id, chrono::Utc::now().timestamp_nanos()),
            price,
            quantity,
            side: order.side.clone(),
            timestamp: Some(chrono::Utc::now()),
        };

        info!(
            "Trade executed: {} {} @ {} (quantity: {})",
            trade.id,
            match trade.side {
                OrderSide::Buy => "BUY",
                OrderSide::Sell => "SELL",
            },
            trade.price,
            trade.quantity
        );

        if let Err(e) = self.trade_tx.send(trade) {
            error!("Failed to send trade: {}", e);
            return Err(BacktestError::Communication(format!(
                "Failed to send trade: {}",
                e
            )));
        }

        Ok(())
    }

    /// 发送订单更新
    async fn send_order_update(&self, order: Order) -> Result<()> {
        if let Err(e) = self.order_update_tx.send(order) {
            error!("Failed to send order update: {}", e);
            return Err(BacktestError::Communication(format!(
                "Failed to send order update: {}",
                e
            )));
        }

        Ok(())
    }

    /// 取消订单
    pub async fn cancel_order(&mut self, order_id: &str) -> Result<()> {
        if let Some(mut order) = self.pending_orders.remove(order_id) {
            order.status = OrderStatus::Cancelled;
            self.orderbook.remove_order(order_id);
            self.send_order_update(order).await?;
            info!("Order cancelled: {}", order_id);
        }

        Ok(())
    }

    /// 获取订单簿快照
    pub fn get_orderbook_snapshot(
        &self,
    ) -> (
        std::collections::BTreeMap<Price, f64>,
        std::collections::BTreeMap<Price, f64>,
    ) {
        self.orderbook.snapshot()
    }
}
