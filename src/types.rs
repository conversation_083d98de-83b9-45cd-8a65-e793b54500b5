use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use std::collections::BTreeMap;

/// 交易所类型
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum Exchange {
    Binance,
    Okx,
    Bybit,
}

/// 订单类型
#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub enum OrderType {
    Market,
    Limit,
}

/// 订单方向
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum OrderSide {
    Buy,
    Sell,
}

/// 订单状态
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum OrderStatus {
    Pending,
    PartiallyFilled,
    Filled,
    Cancelled,
}

/// 价格类型 - 使用有序的包装器
#[derive(Debu<PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, PartialEq, PartialOrd, Serialize, Deserialize)]
pub struct Price(pub f64);

impl Price {
    pub fn new(value: f64) -> Self {
        Self(value)
    }

    pub fn value(&self) -> f64 {
        self.0
    }
}

impl Eq for Price {}

impl Ord for Price {
    fn cmp(&self, other: &Self) -> std::cmp::Ordering {
        self.0
            .partial_cmp(&other.0)
            .unwrap_or(std::cmp::Ordering::Equal)
    }
}

impl From<f64> for Price {
    fn from(value: f64) -> Self {
        Self(value)
    }
}

impl From<Price> for f64 {
    fn from(price: Price) -> Self {
        price.0
    }
}

impl std::fmt::Display for Price {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        write!(f, "{}", self.0)
    }
}

impl std::ops::Add for Price {
    type Output = Self;

    fn add(self, other: Self) -> Self {
        Self(self.0 + other.0)
    }
}

impl std::ops::Sub for Price {
    type Output = Self;

    fn sub(self, other: Self) -> Self {
        Self(self.0 - other.0)
    }
}

impl std::ops::Mul<f64> for Price {
    type Output = Self;

    fn mul(self, other: f64) -> Self {
        Self(self.0 * other)
    }
}

impl std::ops::Div<f64> for Price {
    type Output = Self;

    fn div(self, other: f64) -> Self {
        Self(self.0 / other)
    }
}

/// 数量和更新ID类型
pub type Quantity = f64;
pub type UpdateId = u64;

/// 订单簿快照
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct OrderBookSnapshot {
    pub timestamp: DateTime<Utc>,
    pub update_id: Option<UpdateId>,
    pub bids: BTreeMap<Price, Quantity>,
    pub asks: BTreeMap<Price, Quantity>,
}

/// BBO (Best Bid Offer)
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Bbo {
    pub update_id: UpdateId,
    pub bid_price: Price,
    pub bid_quantity: Quantity,
    pub ask_price: Price,
    pub ask_quantity: Quantity,
}

/// BookTicker数据 - 来自CSV文件的实时最优买卖价格数据
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BookTicker {
    /// 更新ID
    pub update_id: UpdateId,
    /// 最优买价
    pub best_bid_price: Price,
    /// 最优买量
    pub best_bid_qty: Quantity,
    /// 最优卖价
    pub best_ask_price: Price,
    /// 最优卖量
    pub best_ask_qty: Quantity,
    /// 交易时间戳（毫秒）
    pub transaction_time: u64,
    /// 事件时间戳（毫秒）
    pub event_time: u64,
}

impl BookTicker {
    /// 创建新的BookTicker实例
    pub fn new(
        update_id: UpdateId,
        best_bid_price: Price,
        best_bid_qty: Quantity,
        best_ask_price: Price,
        best_ask_qty: Quantity,
        transaction_time: u64,
        event_time: u64,
    ) -> Self {
        Self {
            update_id,
            best_bid_price,
            best_bid_qty,
            best_ask_price,
            best_ask_qty,
            transaction_time,
            event_time,
        }
    }

    /// 获取买卖价差
    pub fn spread(&self) -> Price {
        self.best_ask_price - self.best_bid_price
    }

    /// 获取中间价
    pub fn mid_price(&self) -> Price {
        (self.best_bid_price + self.best_ask_price) / 2.0
    }

    /// 转换为DateTime格式的交易时间
    pub fn transaction_datetime(&self) -> DateTime<Utc> {
        DateTime::from_timestamp_millis(self.transaction_time as i64).unwrap_or_else(|| Utc::now())
    }

    /// 转换为DateTime格式的事件时间
    pub fn event_datetime(&self) -> DateTime<Utc> {
        DateTime::from_timestamp_millis(self.event_time as i64).unwrap_or_else(|| Utc::now())
    }

    /// 转换为BBO格式（兼容性）
    pub fn to_bbo(&self) -> Bbo {
        Bbo {
            update_id: self.update_id,
            bid_price: self.best_bid_price,
            bid_quantity: self.best_bid_qty,
            ask_price: self.best_ask_price,
            ask_quantity: self.best_ask_qty,
        }
    }
}

/// 交易记录
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Trade {
    pub id: String,
    pub price: Price,
    pub quantity: Quantity,
    pub side: OrderSide,
    pub timestamp: Option<DateTime<Utc>>,
}

/// 订单
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Order {
    pub id: String,
    pub order_type: OrderType,
    pub side: OrderSide,
    pub price: Option<Price>,
    pub quantity: Quantity,
    pub status: OrderStatus,
    pub timestamp: DateTime<Utc>,
}

/// 市场数据类型
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum MarketData {
    OrderBook(OrderBookSnapshot),
    Bbo(Bbo),
    Trade(Trade),
    BookTicker(BookTicker),
}

/// WebSocket订阅类型
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Eq, Hash)]
pub enum SubscriptionType {
    OrderBook,
    Bbo,
    Trade,
    Indicators,
    BookTicker,
}

/// 时间屏障类型
#[derive(Debug, Clone)]
pub enum TimeBarrier {
    Timestamp(DateTime<Utc>),
    UpdateId(UpdateId),
}
