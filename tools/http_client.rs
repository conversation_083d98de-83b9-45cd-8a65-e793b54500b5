use clap::{Arg, Command};
use reqwest;
use serde_json::{json, Value};

use tokio;

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    let matches = Command::new("HTTP Client Tool")
        .version("1.0")
        .about("HTTP client tool for testing backtest framework API")
        .arg(
            Arg::new("host")
                .long("host")
                .value_name("HOST")
                .help("Server host")
                .default_value("127.0.0.1"),
        )
        .arg(
            Arg::new("port")
                .short('p')
                .long("port")
                .value_name("PORT")
                .help("Server port")
                .default_value("8081"),
        )
        .arg(
            Arg::new("endpoint")
                .short('e')
                .long("endpoint")
                .value_name("ENDPOINT")
                .help("API endpoint to test")
                .required(true)
                .value_parser([
                    "health",
                    "config",
                    "orderbook",
                    "orders",
                    "trades",
                    "indicators",
                    "all",
                ]),
        )
        .arg(
            Arg::new("method")
                .short('m')
                .long("method")
                .value_name("METHOD")
                .help("HTTP method")
                .default_value("GET")
                .value_parser(["GET", "POST", "PUT", "DELETE"]),
        )
        .arg(
            Arg::new("data")
                .short('d')
                .long("data")
                .value_name("JSON_DATA")
                .help("JSON data for POST/PUT requests"),
        )
        .arg(
            Arg::new("verbose")
                .short('v')
                .long("verbose")
                .help("Verbose output")
                .action(clap::ArgAction::SetTrue),
        )
        .get_matches();

    let host = matches.get_one::<String>("host").unwrap();
    let port = matches.get_one::<String>("port").unwrap();
    let endpoint = matches.get_one::<String>("endpoint").unwrap();
    let method = matches.get_one::<String>("method").unwrap();
    let data = matches.get_one::<String>("data");
    let verbose = matches.get_flag("verbose");

    let base_url = format!("http://{}:{}", host, port);
    let client = reqwest::Client::new();

    if endpoint == "all" {
        println!("🚀 Testing all endpoints...\n");
        test_all_endpoints(&client, &base_url, verbose).await?;
    } else {
        test_single_endpoint(&client, &base_url, endpoint, method, data, verbose).await?;
    }

    Ok(())
}

async fn test_all_endpoints(
    client: &reqwest::Client,
    base_url: &str,
    verbose: bool,
) -> Result<(), Box<dyn std::error::Error>> {
    let endpoints = vec![
        ("health", "GET", None),
        ("config", "GET", None),
        ("orderbook", "GET", None),
        ("orders", "GET", None),
        ("trades", "GET", None),
        ("indicators", "GET", None),
    ];

    for (endpoint, method, data) in endpoints {
        println!("📡 Testing {} {}...", method, endpoint);
        match test_single_endpoint(client, base_url, endpoint, method, data, verbose).await {
            Ok(_) => println!("✅ {} passed\n", endpoint),
            Err(e) => println!("❌ {} failed: {}\n", endpoint, e),
        }
    }

    // Test POST order creation
    println!("📡 Testing POST order creation...");
    let order_data = json!({
        "symbol": "BTCUSDT",
        "side": "Buy",
        "order_type": "Limit",
        "quantity": 1.0,
        "price": 45000.0
    });

    match test_single_endpoint(
        client,
        base_url,
        "orders",
        "POST",
        Some(&order_data.to_string()),
        verbose,
    )
    .await
    {
        Ok(_) => println!("✅ POST order creation passed\n"),
        Err(e) => println!("❌ POST order creation failed: {}\n", e),
    }

    Ok(())
}

async fn test_single_endpoint(
    client: &reqwest::Client,
    base_url: &str,
    endpoint: &str,
    method: &str,
    data: Option<&String>,
    verbose: bool,
) -> Result<(), Box<dyn std::error::Error>> {
    let url = format!("{}/api/v1/{}", base_url, endpoint);

    if verbose {
        println!("🔗 URL: {}", url);
        println!("📋 Method: {}", method);
        if let Some(d) = data {
            println!("📦 Data: {}", d);
        }
    }

    let start = std::time::Instant::now();

    let response = match method {
        "GET" => client.get(&url).send().await?,
        "POST" => {
            let mut req = client.post(&url);
            if let Some(json_data) = data {
                req = req
                    .header("Content-Type", "application/json")
                    .body(json_data.clone());
            }
            req.send().await?
        }
        "PUT" => {
            let mut req = client.put(&url);
            if let Some(json_data) = data {
                req = req
                    .header("Content-Type", "application/json")
                    .body(json_data.clone());
            }
            req.send().await?
        }
        "DELETE" => client.delete(&url).send().await?,
        _ => return Err("Unsupported HTTP method".into()),
    };

    let duration = start.elapsed();
    let status = response.status();
    let headers = response.headers().clone();
    let body = response.text().await?;

    // Print results
    println!("📊 Response Status: {}", status);
    println!("⏱️  Response Time: {:?}", duration);

    if verbose {
        println!("📋 Response Headers:");
        for (name, value) in headers.iter() {
            println!("   {}: {:?}", name, value);
        }
    }

    // Try to parse and pretty-print JSON
    match serde_json::from_str::<Value>(&body) {
        Ok(json) => {
            println!("📄 Response Body:");
            println!("{}", serde_json::to_string_pretty(&json)?);
        }
        Err(_) => {
            println!("📄 Response Body (raw):");
            println!("{}", body);
        }
    }

    if !status.is_success() {
        return Err(format!("HTTP error: {}", status).into());
    }

    Ok(())
}
