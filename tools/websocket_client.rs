use clap::{Arg, Command};
use futures_util::{SinkExt, StreamExt};
use serde_json::json;
use std::time::Duration;
use tokio::time::{interval, timeout};
use tokio_tungstenite::{connect_async, tungstenite::protocol::Message};

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    let matches = Command::new("WebSocket Client Tool")
        .version("1.0")
        .about("WebSocket client tool for testing backtest framework")
        .arg(
            Arg::new("host")
                .long("host")
                .value_name("HOST")
                .help("Server host")
                .default_value("127.0.0.1"),
        )
        .arg(
            Arg::new("port")
                .short('p')
                .long("port")
                .value_name("PORT")
                .help("Server port")
                .default_value("8080"),
        )
        .arg(
            Arg::new("mode")
                .short('m')
                .long("mode")
                .value_name("MODE")
                .help("Test mode")
                .required(true)
                .value_parser(["connect", "subscribe", "interactive", "stress", "ping"]),
        )
        .arg(
            Arg::new("channels")
                .short('c')
                .long("channels")
                .value_name("CHANNELS")
                .help("Channels to subscribe to (comma-separated)")
                .default_value("market_data,trades,orderbook"),
        )
        .arg(
            Arg::new("duration")
                .short('d')
                .long("duration")
                .value_name("SECONDS")
                .help("Test duration in seconds")
                .default_value("30"),
        )
        .arg(
            Arg::new("verbose")
                .short('v')
                .long("verbose")
                .help("Verbose output")
                .action(clap::ArgAction::SetTrue),
        )
        .get_matches();

    let host = matches.get_one::<String>("host").unwrap();
    let port = matches.get_one::<String>("port").unwrap();
    let mode = matches.get_one::<String>("mode").unwrap();
    let channels = matches.get_one::<String>("channels").unwrap();
    let duration: u64 = matches.get_one::<String>("duration").unwrap().parse()?;
    let verbose = matches.get_flag("verbose");

    let url = format!("ws://{}:{}", host, port);

    match mode.as_str() {
        "connect" => test_connection(&url, verbose).await?,
        "subscribe" => test_subscription(&url, channels, duration, verbose).await?,
        "interactive" => interactive_mode(&url, verbose).await?,
        "stress" => stress_test(&url, duration, verbose).await?,
        "ping" => ping_test(&url, duration, verbose).await?,
        _ => return Err("Unknown mode".into()),
    }

    Ok(())
}

async fn test_connection(url: &str, verbose: bool) -> Result<(), Box<dyn std::error::Error>> {
    println!("🔗 Testing WebSocket connection to {}", url);

    let start = std::time::Instant::now();
    let (ws_stream, response) = connect_async(url).await?;
    let duration = start.elapsed();

    println!("✅ Connected successfully in {:?}", duration);

    if verbose {
        println!("📋 Response status: {}", response.status());
        println!("📋 Response headers:");
        for (name, value) in response.headers() {
            println!("   {}: {:?}", name, value);
        }
    }

    let (mut write, mut read) = ws_stream.split();

    // Send a simple ping
    write.send(Message::Ping(vec![])).await?;
    println!("📤 Sent ping");

    // Wait for pong
    if let Some(msg) = timeout(Duration::from_secs(5), read.next()).await? {
        match msg? {
            Message::Pong(_) => println!("📥 Received pong"),
            other => println!("📥 Received: {:?}", other),
        }
    }

    println!("🔌 Closing connection");
    write.close().await?;

    Ok(())
}

async fn test_subscription(
    url: &str,
    channels: &str,
    duration: u64,
    verbose: bool,
) -> Result<(), Box<dyn std::error::Error>> {
    println!("📡 Testing WebSocket subscription to {}", url);
    println!("📋 Channels: {}", channels);
    println!("⏱️  Duration: {} seconds", duration);

    let (ws_stream, _) = connect_async(url).await?;
    let (mut write, mut read) = ws_stream.split();

    // Subscribe to channels
    let channel_list: Vec<&str> = channels.split(',').collect();
    let subscribe_msg = json!({
        "type": "subscribe",
        "data": {
            "channels": channel_list
        }
    });

    write.send(Message::Text(subscribe_msg.to_string())).await?;
    println!("📤 Sent subscription: {}", subscribe_msg);

    let mut message_count = 0;
    let start_time = std::time::Instant::now();

    while start_time.elapsed().as_secs() < duration {
        if let Ok(Some(msg)) = timeout(Duration::from_secs(1), read.next()).await {
            match msg? {
                Message::Text(text) => {
                    message_count += 1;
                    if verbose {
                        println!("📥 Message {}: {}", message_count, text);
                    } else {
                        if message_count % 10 == 0 {
                            println!("📥 Received {} messages", message_count);
                        }
                    }
                }
                Message::Ping(_) => {
                    write.send(Message::Pong(vec![])).await?;
                    if verbose {
                        println!("🏓 Ping/Pong");
                    }
                }
                other => {
                    if verbose {
                        println!("📥 Other message: {:?}", other);
                    }
                }
            }
        }
    }

    println!("📊 Total messages received: {}", message_count);
    println!(
        "📊 Messages per second: {:.2}",
        message_count as f64 / duration as f64
    );

    write.close().await?;
    Ok(())
}

async fn interactive_mode(url: &str, verbose: bool) -> Result<(), Box<dyn std::error::Error>> {
    println!("🎮 Interactive WebSocket mode");
    println!("💡 Commands:");
    println!("   subscribe <channels>  - Subscribe to channels");
    println!("   unsubscribe <channels> - Unsubscribe from channels");
    println!("   ping                  - Send ping");
    println!("   quit                  - Exit");

    let (ws_stream, _) = connect_async(url).await?;
    let (mut write, mut read) = ws_stream.split();

    // Spawn task to handle incoming messages
    let read_task = tokio::spawn(async move {
        while let Some(msg) = read.next().await {
            match msg {
                Ok(Message::Text(text)) => println!("📥 Received: {}", text),
                Ok(Message::Ping(_)) => println!("🏓 Received ping"),
                Ok(Message::Pong(_)) => println!("🏓 Received pong"),
                Ok(other) => {
                    if verbose {
                        println!("📥 Other: {:?}", other);
                    }
                }
                Err(e) => {
                    println!("❌ Error: {}", e);
                    break;
                }
            }
        }
    });

    // Handle user input
    loop {
        println!("\n> ");
        let mut input = String::new();
        std::io::stdin().read_line(&mut input)?;
        let input = input.trim();

        if input.is_empty() {
            continue;
        }

        let parts: Vec<&str> = input.split_whitespace().collect();
        match parts[0] {
            "quit" => break,
            "ping" => {
                write.send(Message::Ping(vec![])).await?;
                println!("📤 Sent ping");
            }
            "subscribe" => {
                if parts.len() > 1 {
                    let channels: Vec<&str> = parts[1].split(',').collect();
                    let msg = json!({
                        "type": "subscribe",
                        "data": { "channels": channels }
                    });
                    write.send(Message::Text(msg.to_string())).await?;
                    println!("📤 Sent: {}", msg);
                } else {
                    println!("❌ Usage: subscribe <channels>");
                }
            }
            "unsubscribe" => {
                if parts.len() > 1 {
                    let channels: Vec<&str> = parts[1].split(',').collect();
                    let msg = json!({
                        "type": "unsubscribe",
                        "data": { "channels": channels }
                    });
                    write.send(Message::Text(msg.to_string())).await?;
                    println!("📤 Sent: {}", msg);
                } else {
                    println!("❌ Usage: unsubscribe <channels>");
                }
            }
            _ => {
                // Send as raw message
                write.send(Message::Text(input.to_string())).await?;
                println!("📤 Sent: {}", input);
            }
        }
    }

    read_task.abort();
    write.close().await?;
    Ok(())
}

async fn stress_test(
    url: &str,
    duration: u64,
    verbose: bool,
) -> Result<(), Box<dyn std::error::Error>> {
    println!("🔥 WebSocket stress test for {} seconds", duration);

    let (ws_stream, _) = connect_async(url).await?;
    let (mut write, mut read) = ws_stream.split();

    let mut sent_count = 0;
    let mut received_count = 0;
    let start_time = std::time::Instant::now();

    // Spawn message sender
    let write_task = tokio::spawn(async move {
        let mut interval = interval(Duration::from_millis(100));
        let mut counter = 0;

        while start_time.elapsed().as_secs() < duration {
            interval.tick().await;
            counter += 1;

            let msg = json!({
                "type": "test_message",
                "id": counter,
                "timestamp": chrono::Utc::now().to_rfc3339()
            });

            if write.send(Message::Text(msg.to_string())).await.is_err() {
                break;
            }

            sent_count += 1;
        }

        sent_count
    });

    // Handle incoming messages
    while start_time.elapsed().as_secs() < duration {
        if let Ok(Some(msg)) = timeout(Duration::from_millis(100), read.next()).await {
            match msg? {
                Message::Text(_) => {
                    received_count += 1;
                    if verbose && received_count % 50 == 0 {
                        println!("📊 Sent: {}, Received: {}", sent_count, received_count);
                    }
                }
                _ => {}
            }
        }
    }

    let final_sent = write_task.await?;

    println!("📊 Stress test results:");
    println!("   Messages sent: {}", final_sent);
    println!("   Messages received: {}", received_count);
    println!(
        "   Send rate: {:.2} msg/s",
        final_sent as f64 / duration as f64
    );
    println!(
        "   Receive rate: {:.2} msg/s",
        received_count as f64 / duration as f64
    );

    Ok(())
}

async fn ping_test(
    url: &str,
    duration: u64,
    verbose: bool,
) -> Result<(), Box<dyn std::error::Error>> {
    println!("🏓 WebSocket ping test for {} seconds", duration);

    let (ws_stream, _) = connect_async(url).await?;
    let (mut write, mut read) = ws_stream.split();

    let mut ping_count = 0;
    let mut pong_count = 0;
    let mut total_latency = Duration::new(0, 0);
    let start_time = std::time::Instant::now();

    let mut interval = interval(Duration::from_secs(1));

    while start_time.elapsed().as_secs() < duration {
        interval.tick().await;

        let ping_time = std::time::Instant::now();
        write.send(Message::Ping(vec![])).await?;
        ping_count += 1;

        if verbose {
            println!("📤 Ping #{}", ping_count);
        }

        // Wait for pong
        if let Ok(Some(msg)) = timeout(Duration::from_secs(2), read.next()).await {
            match msg? {
                Message::Pong(_) => {
                    let latency = ping_time.elapsed();
                    total_latency += latency;
                    pong_count += 1;

                    if verbose {
                        println!("📥 Pong #{} (latency: {:?})", pong_count, latency);
                    }
                }
                _ => {}
            }
        }
    }

    println!("📊 Ping test results:");
    println!("   Pings sent: {}", ping_count);
    println!("   Pongs received: {}", pong_count);
    println!(
        "   Success rate: {:.1}%",
        (pong_count as f64 / ping_count as f64) * 100.0
    );

    if pong_count > 0 {
        let avg_latency = total_latency / pong_count as u32;
        println!("   Average latency: {:?}", avg_latency);
    }

    write.close().await?;
    Ok(())
}
