#!/bin/bash

echo "🚀 Starting BookTicker subscription test..."

# 启动框架（后台运行）
echo "📡 Starting backtest framework..."
cargo run --bin backtest &
FRAMEWORK_PID=$!

# 等待框架启动
echo "⏳ Waiting for framework to start..."
sleep 3

# 启动客户端
echo "📱 Starting WebSocket client..."
cargo run --bin websocket_client -- -m subscribe -s BookTicker -d 45 -v

# 清理
echo "🧹 Cleaning up..."
kill $FRAMEWORK_PID 2>/dev/null
wait $FRAMEWORK_PID 2>/dev/null

echo "✅ Test completed!"
