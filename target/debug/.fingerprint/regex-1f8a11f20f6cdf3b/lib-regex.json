{"rustc": 12249123186621193792, "features": "[\"std\", \"unicode-case\", \"unicode-perl\"]", "declared_features": "[\"default\", \"logging\", \"pattern\", \"perf\", \"perf-backtrack\", \"perf-cache\", \"perf-dfa\", \"perf-dfa-full\", \"perf-inline\", \"perf-literal\", \"perf-onepass\", \"std\", \"unicode\", \"unicode-age\", \"unicode-bool\", \"unicode-case\", \"unicode-gencat\", \"unicode-perl\", \"unicode-script\", \"unicode-segment\", \"unstable\", \"use_std\"]", "target": 5796931310894148030, "profile": 15657897354478470176, "path": 9497543129350581631, "deps": [[555019317135488525, "regex_automata", false, 16077428818959007717], [9408802513701742484, "regex_syntax", false, 13331557886562749925]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/regex-1f8a11f20f6cdf3b/dep-lib-regex", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}