{"rustc": 12249123186621193792, "features": "[\"color\", \"default\", \"error-context\", \"help\", \"std\", \"suggestions\", \"usage\"]", "declared_features": "[\"cargo\", \"color\", \"debug\", \"default\", \"deprecated\", \"derive\", \"env\", \"error-context\", \"help\", \"std\", \"string\", \"suggestions\", \"unicode\", \"unstable-derive-ui-tests\", \"unstable-doc\", \"unstable-ext\", \"unstable-markdown\", \"unstable-styles\", \"unstable-v5\", \"usage\", \"wrap_help\"]", "target": 4238846637535193678, "profile": 15599109589607159429, "path": 1767968415509516068, "deps": [[14814905555676593471, "clap_builder", false, 7076334458093226309]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/clap-5180f24251de87ec/dep-lib-clap", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}