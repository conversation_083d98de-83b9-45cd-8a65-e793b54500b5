{"rustc": 12249123186621193792, "features": "[\"data-encoding\", \"handshake\", \"http\", \"httparse\", \"sha1\", \"url\"]", "declared_features": "[\"__rustls-tls\", \"data-encoding\", \"default\", \"handshake\", \"http\", \"httparse\", \"native-tls\", \"native-tls-crate\", \"native-tls-vendored\", \"rustls\", \"rustls-native-certs\", \"rustls-tls-native-roots\", \"rustls-tls-webpki-roots\", \"sha1\", \"url\", \"webpki-roots\"]", "target": 1270341572213479472, "profile": 15657897354478470176, "path": 79565753090462504, "deps": [[99287295355353247, "data_encoding", false, 5760902787155560083], [3150220818285335163, "url", false, 17548353351790019349], [3712811570531045576, "byteorder", false, 14036780057639340590], [4359956005902820838, "utf8", false, 1006284819076995372], [4405182208873388884, "http", false, 3138634277232414067], [5986029879202738730, "log", false, 17669908679007362022], [6163892036024256188, "httparse", false, 8944474240299470193], [8008191657135824715, "thiserror", false, 11029667628810200740], [10724389056617919257, "sha1", false, 8429171394705474675], [13208667028893622512, "rand", false, 9175638045805873974], [16066129441945555748, "bytes", false, 6207963639048186404]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/tungstenite-d8fba9b23c3c4f52/dep-lib-tungstenite", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}