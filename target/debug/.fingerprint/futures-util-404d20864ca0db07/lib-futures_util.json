{"rustc": 12249123186621193792, "features": "[\"alloc\", \"async-await\", \"async-await-macro\", \"channel\", \"futures-channel\", \"futures-io\", \"futures-macro\", \"futures-sink\", \"io\", \"memchr\", \"sink\", \"slab\", \"std\"]", "declared_features": "[\"alloc\", \"async-await\", \"async-await-macro\", \"bilock\", \"cfg-target-has-atomic\", \"channel\", \"compat\", \"default\", \"futures-channel\", \"futures-io\", \"futures-macro\", \"futures-sink\", \"futures_01\", \"io\", \"io-compat\", \"memchr\", \"portable-atomic\", \"sink\", \"slab\", \"std\", \"tokio-io\", \"unstable\", \"write-all-vectored\"]", "target": 1788798584831431502, "profile": 13318305459243126790, "path": 17255146969469356042, "deps": [[5103565458935487, "futures_io", false, 235888840586688924], [1615478164327904835, "pin_utils", false, 10109531136078960170], [1811549171721445101, "futures_channel", false, 8508874114450713953], [1906322745568073236, "pin_project_lite", false, 17027159812132020487], [5451793922601807560, "slab", false, 16295252074842697811], [7013762810557009322, "futures_sink", false, 7307640179168296514], [7620660491849607393, "futures_core", false, 136706416532143871], [10565019901765856648, "futures_macro", false, 2014155782816193861], [15932120279885307830, "memchr", false, 13662222608380515396], [16240732885093539806, "futures_task", false, 15277454055768739577]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/futures-util-404d20864ca0db07/dep-lib-futures_util", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}