{"rustc": 12249123186621193792, "features": "[\"alloc\", \"derive\"]", "declared_features": "[\"alloc\", \"default\", \"derive\"]", "target": 723370850876025358, "profile": 15657897354478470176, "path": 5808174311384438039, "deps": [[4022439902832367970, "zerofrom_derive", false, 13136735678929877262]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/zerofrom-e12c7140962f0ad8/dep-lib-zerofrom", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}