{"rustc": 12249123186621193792, "features": "[\"attributes\", \"default\", \"log\", \"std\", \"tracing-attributes\"]", "declared_features": "[\"async-await\", \"attributes\", \"default\", \"log\", \"log-always\", \"max_level_debug\", \"max_level_error\", \"max_level_info\", \"max_level_off\", \"max_level_trace\", \"max_level_warn\", \"release_max_level_debug\", \"release_max_level_error\", \"release_max_level_info\", \"release_max_level_off\", \"release_max_level_trace\", \"release_max_level_warn\", \"std\", \"tracing-attributes\", \"valuable\"]", "target": 5568135053145998517, "profile": 6355579909791343455, "path": 5376888363511088426, "deps": [[325572602735163265, "tracing_attributes", false, 636768404928617603], [1906322745568073236, "pin_project_lite", false, 17027159812132020487], [3424551429995674438, "tracing_core", false, 6062521561672860000], [5986029879202738730, "log", false, 17669908679007362022]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/tracing-948d747c36d919b2/dep-lib-tracing", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}