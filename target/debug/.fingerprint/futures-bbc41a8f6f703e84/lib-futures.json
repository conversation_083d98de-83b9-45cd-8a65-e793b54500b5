{"rustc": 12249123186621193792, "features": "[\"alloc\", \"async-await\", \"default\", \"executor\", \"futures-executor\", \"std\"]", "declared_features": "[\"alloc\", \"async-await\", \"bilock\", \"cfg-target-has-atomic\", \"compat\", \"default\", \"executor\", \"futures-executor\", \"io-compat\", \"std\", \"thread-pool\", \"unstable\", \"write-all-vectored\"]", "target": 7465627196321967167, "profile": 13318305459243126790, "path": 4857201165016418111, "deps": [[5103565458935487, "futures_io", false, 235888840586688924], [1811549171721445101, "futures_channel", false, 8508874114450713953], [7013762810557009322, "futures_sink", false, 7307640179168296514], [7620660491849607393, "futures_core", false, 136706416532143871], [10629569228670356391, "futures_util", false, 15726883543517987096], [12779779637805422465, "futures_executor", false, 3304841539928373210], [16240732885093539806, "futures_task", false, 15277454055768739577]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/futures-bbc41a8f6f703e84/dep-lib-futures", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}