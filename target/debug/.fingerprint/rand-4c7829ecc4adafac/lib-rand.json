{"rustc": 12249123186621193792, "features": "[\"alloc\", \"default\", \"getrandom\", \"libc\", \"rand_chacha\", \"std\", \"std_rng\"]", "declared_features": "[\"alloc\", \"default\", \"getrandom\", \"libc\", \"log\", \"min_const_gen\", \"nightly\", \"packed_simd\", \"rand_chacha\", \"serde\", \"serde1\", \"simd_support\", \"small_rng\", \"std\", \"std_rng\"]", "target": 8827111241893198906, "profile": 15657897354478470176, "path": 17665849812706888886, "deps": [[1573238666360410412, "rand_chacha", false, 2365115279503041585], [4684437522915235464, "libc", false, 5296296229996544248], [18130209639506977569, "rand_core", false, 10856979340156628676]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/rand-4c7829ecc4adafac/dep-lib-rand", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}